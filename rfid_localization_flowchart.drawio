<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="RFID定位流程图" id="rfid-localization-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入信号层叠 -->
        <mxCell id="input-stack" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="50" y="200" width="120" height="200" as="geometry" />
        </mxCell>

        <!-- 5层信号输入 -->
        <mxCell id="signal1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="0" y="0" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="5" y="25" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="10" y="50" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="15" y="75" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal5" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="20" y="100" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 输入标签 -->
        <mxCell id="input-label" value="N路RFID原始信号&#xa;(多天线×多标签×多时间窗)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="30" y="420" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 信号预处理模块 -->
        <mxCell id="preprocessing" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="250" y="180" width="180" height="240" as="geometry" />
        </mxCell>

        <!-- 信号波形图 (原始信号) -->
        <mxCell id="signal-wave1" value="" style="curved=1;endArrow=none;html=1;strokeColor=#82B366;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="275" y="220" as="sourcePoint" />
            <mxPoint x="325" y="220" as="targetPoint" />
            <Array as="points">
              <mxPoint x="285" y="210" />
              <mxPoint x="295" y="230" />
              <mxPoint x="305" y="210" />
              <mxPoint x="315" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 滤波后信号波形 -->
        <mxCell id="signal-wave2" value="" style="curved=1;endArrow=none;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="355" y="220" as="sourcePoint" />
            <mxPoint x="405" y="220" as="targetPoint" />
            <Array as="points">
              <mxPoint x="365" y="215" />
              <mxPoint x="375" y="225" />
              <mxPoint x="385" y="215" />
              <mxPoint x="395" y="225" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 差分运算符号 -->
        <mxCell id="delta-symbol" value="Δ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontWeight=bold;fontColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="280" y="270" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 减法运算图形 -->
        <mxCell id="minus-symbol" value="−" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontWeight=bold;fontColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="320" y="275" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 差分结果波形 -->
        <mxCell id="diff-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="280" as="sourcePoint" />
            <mxPoint x="400" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="360" y="275" />
              <mxPoint x="370" y="285" />
              <mxPoint x="380" y="275" />
              <mxPoint x="390" y="285" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 非线性变换函数曲线 -->
        <mxCell id="nonlinear-curve" value="" style="curved=1;endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="350" as="sourcePoint" />
            <mxPoint x="380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="300" y="360" />
              <mxPoint x="320" y="345" />
              <mxPoint x="340" y="342" />
              <mxPoint x="360" y="341" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- σ激活函数符号 -->
        <mxCell id="sigma-symbol" value="σ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontWeight=bold;fontColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="390" y="340" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 预处理标签 -->
        <mxCell id="preprocessing-label" value="信号预处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="290" y="430" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 异构图构建模块 -->
        <mxCell id="graph-construction" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="500" y="180" width="220" height="240" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag-node1" value="T1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;" vertex="1" parent="1">
          <mxGeometry x="530" y="210" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tag-node2" value="T2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;" vertex="1" parent="1">
          <mxGeometry x="630" y="210" width="40" height="40" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna-node1" value="A1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="530" y="320" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="antenna-node2" value="A2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="630" y="320" width="40" height="40" as="geometry" />
        </mxCell>

        <!-- 边关系 -->
        <!-- T-T边 (标签-标签) -->
        <mxCell id="edge-tt" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=2;dashed=0;" edge="1" parent="1" source="tag-node1" target="tag-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="280" as="sourcePoint" />
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T-A边 (标签-天线) -->
        <mxCell id="edge-ta1" value="" style="endArrow=classic;html=1;strokeColor=#0066CC;strokeWidth=2;" edge="1" parent="1" source="tag-node1" target="antenna-node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="280" as="sourcePoint" />
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge-ta2" value="" style="endArrow=classic;html=1;strokeColor=#0066CC;strokeWidth=2;" edge="1" parent="1" source="tag-node2" target="antenna-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="280" as="sourcePoint" />
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-T边 (天线-标签) -->
        <mxCell id="edge-at1" value="" style="endArrow=classic;html=1;strokeColor=#66B2FF;strokeWidth=1;dashed=1;" edge="1" parent="1" source="antenna-node1" target="tag-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="280" as="sourcePoint" />
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-A边 (天线-天线) -->
        <mxCell id="edge-aa" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;dashed=1;dashPattern=5 5;" edge="1" parent="1" source="antenna-node1" target="antenna-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="280" as="sourcePoint" />
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 边类型说明 -->
        <mxCell id="edge-legend" value="T-T: 空间相关性&#xa;T-A: 信号观测&#xa;A-T: 覆盖影响&#xa;A-A: 天线协同" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="520" y="370" width="100" height="40" as="geometry" />
        </mxCell>

        <!-- 异构图构建标签 -->
        <mxCell id="graph-construction-label" value="异构图构建&#xa;(标签-天线二元图)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="560" y="430" width="100" height="40" as="geometry" />
        </mxCell>

        <!-- 异构图表示学习模块 -->
        <mxCell id="representation-learning" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="800" y="180" width="280" height="240" as="geometry" />
        </mxCell>

        <!-- MLP网络结构 -->
        <!-- 输入层 -->
        <mxCell id="mlp-input1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="820" y="200" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-input2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="820" y="230" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-input3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="820" y="260" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 隐藏层1 -->
        <mxCell id="mlp-hidden1-1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="880" y="190" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="880" y="210" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="880" y="230" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="880" y="250" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="880" y="270" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 隐藏层2 -->
        <mxCell id="mlp-hidden2-1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="930" y="200" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="930" y="220" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="930" y="240" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="930" y="260" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 输出层 -->
        <mxCell id="mlp-output1" value="x" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="980" y="220" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-output2" value="y" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="980" y="240" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- MLP连接线 (简化显示部分连接) -->
        <mxCell id="mlp-conn1" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="250" as="sourcePoint" />
            <mxPoint x="900" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mlp-conn2" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="250" as="sourcePoint" />
            <mxPoint x="900" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mlp-conn3" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="250" as="sourcePoint" />
            <mxPoint x="900" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mlp-conn4" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;" edge="1" parent="1" source="mlp-hidden2-2" target="mlp-output1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="250" as="sourcePoint" />
            <mxPoint x="900" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mlp-conn5" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;" edge="1" parent="1" source="mlp-hidden2-3" target="mlp-output2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="250" as="sourcePoint" />
            <mxPoint x="900" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- HGATv2标注 -->
        <mxCell id="hgatv2-label" value="HGATv2 + MLP融合&#xa;多头注意力机制&#xa;信号可靠性调节" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#D79B00;fillColor=#FFF2CC;" vertex="1" parent="1">
          <mxGeometry x="820" y="320" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 输出标注 -->
        <mxCell id="output-label" value="坐标预测 (x,y)&#xa;置信度评估" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1010" y="225" width="80" height="30" as="geometry" />
        </mxCell>

        <!-- 表示学习标签 -->
        <mxCell id="representation-learning-label" value="异构图表示学习&#xa;(HGATv2)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="890" y="430" width="100" height="40" as="geometry" />
        </mxCell>

        <!-- 流程箭头 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="190" y="300" as="sourcePoint" />
            <mxPoint x="240" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="300" as="sourcePoint" />
            <mxPoint x="490" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="730" y="300" as="sourcePoint" />
            <mxPoint x="790" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 箭头标注 -->
        <mxCell id="arrow1-label" value="滤波/对齐/&#xa;多尺度特征化" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="190" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow2-label" value="构建二元异构图&#xa;四类边关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="440" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow3-label" value="关系感知注意力&#xa;与聚合" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="730" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <!-- 图题 -->
        <mxCell id="figure-title" value="图1 RFID静态定位的异构图注意力管线：从多路RSSI/相位预处理，经T-T/T-A/A-T/A-A边集合的二元异构图构建，到HGATv2表示学习与位置估计" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="200" y="500" width="700" height="40" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
