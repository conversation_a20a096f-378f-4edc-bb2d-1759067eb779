<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="RFID定位流程图" id="rfid-localization-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1800" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入信号层叠 -->
        <mxCell id="input-stack" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="50" y="200" width="120" height="200" as="geometry" />
        </mxCell>

        <!-- 5层信号输入 -->
        <mxCell id="signal1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="0" y="0" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="5" y="25" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="10" y="50" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="15" y="75" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal5" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="20" y="100" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 输入标签 -->
        <mxCell id="input-label" value="N路RFID原始信号&#xa;(多天线×多标签)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="30" y="420" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 信号预处理模块 -->
        <mxCell id="preprocessing" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="250" y="180" width="180" height="240" as="geometry" />
        </mxCell>

        <!-- 简化的波形信号预处理图形 -->
        <mxCell id="simplified-preprocessing-waveform" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="260" y="220" width="160" height="120" as="geometry" />
        </mxCell>

        <!-- 原始噪声信号波形（放大版） -->
        <mxCell id="simplified-original-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#82B366;strokeWidth=4;" edge="1" parent="simplified-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="10" y="40" as="sourcePoint" />
            <mxPoint x="70" y="40" as="targetPoint" />
            <Array as="points">
              <mxPoint x="15" y="20" />
              <mxPoint x="20" y="60" />
              <mxPoint x="25" y="15" />
              <mxPoint x="30" y="65" />
              <mxPoint x="35" y="25" />
              <mxPoint x="40" y="55" />
              <mxPoint x="45" y="30" />
              <mxPoint x="50" y="50" />
              <mxPoint x="55" y="35" />
              <mxPoint x="60" y="45" />
              <mxPoint x="65" y="40" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 信号处理器（简化版） -->
        <mxCell id="simplified-processor" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;fillColor=#DAE8FC;" vertex="1" parent="simplified-preprocessing-waveform">
          <mxGeometry x="75" y="25" width="40" height="30" as="geometry" />
        </mxCell>

        <!-- 处理后的平滑波形（放大版） -->
        <mxCell id="simplified-processed-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=4;" edge="1" parent="simplified-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="125" y="40" as="sourcePoint" />
            <mxPoint x="150" y="40" as="targetPoint" />
            <Array as="points">
              <mxPoint x="130" y="35" />
              <mxPoint x="135" y="45" />
              <mxPoint x="140" y="35" />
              <mxPoint x="145" y="45" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 主要处理流程箭头 -->
        <mxCell id="main-processing-arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="simplified-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="75" y="40" as="sourcePoint" />
            <mxPoint x="75" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="main-processing-arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="simplified-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="115" y="40" as="sourcePoint" />
            <mxPoint x="120" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 对比标注 -->
        <mxCell id="noise-label" value="" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#82B366;" vertex="1" parent="simplified-preprocessing-waveform">
          <mxGeometry x="10" y="70" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="clean-label" value="" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#9673A6;" vertex="1" parent="simplified-preprocessing-waveform">
          <mxGeometry x="120" y="70" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 预处理标签 -->
        <mxCell id="preprocessing-label" value="信号预处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="290" y="430" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 整合的异构图构建与表示学习模块 -->
        <mxCell id="integrated-hetero-module" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="480" y="180" width="420" height="240" as="geometry" />
        </mxCell>

        <!-- 异构图构建部分 -->
        <mxCell id="graph-construction-section" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="500" y="200" width="180" height="200" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag-node1" value="T1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="30" y="30" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="tag-node2" value="T2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="115" y="30" width="35" height="35" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna-node1" value="A1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="30" y="135" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="antenna-node2" value="A2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="115" y="135" width="35" height="35" as="geometry" />
        </mxCell>

        <!-- 边关系 -->
        <!-- T-T边 (标签-标签) -->
        <mxCell id="edge-tt" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=2;dashed=0;" edge="1" parent="graph-construction-section" source="tag-node1" target="tag-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="50" as="sourcePoint" />
            <mxPoint x="110" y="50" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T-A边 (标签-天线) -->
        <mxCell id="edge-ta1" value="" style="endArrow=classic;html=1;strokeColor=#0066CC;strokeWidth=2;" edge="1" parent="graph-construction-section" source="tag-node1" target="antenna-node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="50" y="70" as="sourcePoint" />
            <mxPoint x="50" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge-ta2" value="" style="endArrow=classic;html=1;strokeColor=#0066CC;strokeWidth=2;" edge="1" parent="graph-construction-section" source="tag-node2" target="antenna-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="135" y="70" as="sourcePoint" />
            <mxPoint x="135" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-T边 (天线-标签) -->
        <mxCell id="edge-at1" value="" style="endArrow=classic;html=1;strokeColor=#66B2FF;strokeWidth=1;dashed=1;" edge="1" parent="graph-construction-section" source="antenna-node1" target="tag-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="140" as="sourcePoint" />
            <mxPoint x="110" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-A边 (天线-天线) -->
        <mxCell id="edge-aa" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;dashed=1;dashPattern=5 5;" edge="1" parent="graph-construction-section" source="antenna-node1" target="antenna-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="155" as="sourcePoint" />
            <mxPoint x="110" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>







        <!-- 异构图构建模块标签 -->
        <mxCell id="graph-construction-label" value="异构图构建&#xa;(标签-天线二元异构图)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="560" y="350" width="140" height="40" as="geometry" />
        </mxCell>

        <!-- 合并模块标签 -->
        <mxCell id="merged-module-label" value="异构图特征学习与多模型融合定位预测&#xa;(HGATv2异构图注意力网络 + MLP多层感知机)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1000" y="430" width="280" height="40" as="geometry" />
        </mxCell>



        <!-- 合并的特征学习与多模型融合定位预测模块 -->
        <mxCell id="merged-learning-fusion-module" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="920" y="180" width="370" height="240" as="geometry" />
        </mxCell>





        <!-- 重新设计的特征学习部分 -->
        <mxCell id="redesigned-feature-learning" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="940" y="200" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- 原始图特征（粗糙） -->
        <mxCell id="raw-graph-features" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;fillColor=#E6F3FF;opacity=50;" vertex="1" parent="redesigned-feature-learning">
          <mxGeometry x="5" y="10" width="110" height="8" as="geometry" />
        </mxCell>

        <!-- 迭代学习过程可视化 -->
        <mxCell id="learning-iteration1" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#9673A6;fillColor=#E1D5E7;opacity=70;" vertex="1" parent="redesigned-feature-learning">
          <mxGeometry x="10" y="25" width="100" height="8" as="geometry" />
        </mxCell>

        <mxCell id="learning-iteration2" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#82B366;fillColor=#D5E8D4;opacity=90;" vertex="1" parent="redesigned-feature-learning">
          <mxGeometry x="15" y="40" width="90" height="8" as="geometry" />
        </mxCell>

        <!-- 精炼的最终特征 -->
        <mxCell id="refined-final-features" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#82B366;fillColor=#D5E8D4;" vertex="1" parent="redesigned-feature-learning">
          <mxGeometry x="25" y="55" width="70" height="10" as="geometry" />
        </mxCell>

        <!-- 学习演化箭头 -->
        <mxCell id="evolution-arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="redesigned-feature-learning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="20" as="sourcePoint" />
            <mxPoint x="60" y="23" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="evolution-arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="redesigned-feature-learning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="35" as="sourcePoint" />
            <mxPoint x="60" y="38" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="evolution-arrow3" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="redesigned-feature-learning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="50" as="sourcePoint" />
            <mxPoint x="60" y="53" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- MLP网络结构 -->
        <!-- 输入层 -->
        <mxCell id="mlp-input1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="1080" y="200" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-input2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="1080" y="230" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-input3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="1080" y="260" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 隐藏层1 -->
        <mxCell id="mlp-hidden1-1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1130" y="190" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1130" y="210" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1130" y="230" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1130" y="250" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1130" y="270" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 隐藏层2 -->
        <mxCell id="mlp-hidden2-1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1170" y="200" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1170" y="220" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1170" y="240" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1170" y="260" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 输出层 -->
        <mxCell id="mlp-output1" value="x" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1210" y="220" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-output2" value="y" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1210" y="240" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- MLP全连接网络连接线 -->
        <!-- 输入层到隐藏层1的连接 -->
        <mxCell id="mlp-conn-i1-h11" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h12" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h13" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h14" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h15" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-i2-h11" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h12" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h13" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h14" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h15" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-i3-h11" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h12" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h13" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h14" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h15" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 隐藏层1到隐藏层2的连接 -->
        <mxCell id="mlp-conn-h11-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h11-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h11-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h11-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h12-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h12-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h12-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h12-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h13-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h13-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h13-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h13-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h14-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h14-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h14-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h14-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h15-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h15-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h15-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h15-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 隐藏层2到输出层的连接 -->
        <mxCell id="mlp-conn-h21-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-1" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h21-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-1" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h22-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-2" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h22-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-2" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h23-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-3" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h23-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-3" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h24-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-4" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h24-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-4" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>



        <!-- 融合节点 -->
        <mxCell id="fusion-node" value="⊕" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;fontSize=16;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1240" y="280" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- MLP路径到融合节点的连接 -->
        <mxCell id="mlp-to-fusion" value="" style="endArrow=classic;html=1;strokeColor=#B85450;strokeWidth=2;" edge="1" parent="1" source="mlp-output1" target="fusion-node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1230" y="230" as="sourcePoint" />
            <mxPoint x="1240" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 特征学习到融合节点的连接 -->
        <mxCell id="learning-to-fusion" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=2;" edge="1" parent="1" source="refined-final-features" target="fusion-node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1020" y="265" as="sourcePoint" />
            <mxPoint x="1240" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 最终坐标输出 -->
        <mxCell id="final-x-output" value="x" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1280" y="270" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="final-y-output" value="y" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1280" y="305" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 融合到最终输出的连接 -->
        <mxCell id="fusion-to-x-output" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=2;" edge="1" parent="1" source="fusion-node" target="final-x-output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1270" y="295" as="sourcePoint" />
            <mxPoint x="1280" y="282" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="fusion-to-y-output" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=2;" edge="1" parent="1" source="fusion-node" target="final-y-output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1270" y="295" as="sourcePoint" />
            <mxPoint x="1280" y="317" as="targetPoint" />
          </mxGeometry>
        </mxCell>





        <!-- 流程箭头 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="190" y="300" as="sourcePoint" />
            <mxPoint x="240" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="300" as="sourcePoint" />
            <mxPoint x="490" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="300" as="sourcePoint" />
            <mxPoint x="470" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="890" y="300" as="sourcePoint" />
            <mxPoint x="910" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 箭头标注 -->
        <mxCell id="arrow1-label" value="滤波/对齐/&#xa;多尺度特征化" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="190" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow2-label" value="构建二元异构图&#xa;四类边关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="440" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow3-label" value="异构图&#xa;构建" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="420" y="270" width="50" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow4-label" value="特征学习与&#xa;多模型融合" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="870" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <!-- 图题 -->
        <mxCell id="figure-title" value="图1 面向室内RFID静态定位的异构图注意力模型技术流程：从多路RSSI信号预处理，经标签-天线二元异构图构建，到HGATv2异构图特征学习与多模型融合定位预测" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="200" y="500" width="1000" height="40" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
