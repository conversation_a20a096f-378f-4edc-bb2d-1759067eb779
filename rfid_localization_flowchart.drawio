<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="RFID定位流程图" id="rfid-localization-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1800" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入信号层叠 -->
        <mxCell id="input-stack" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="50" y="200" width="120" height="200" as="geometry" />
        </mxCell>

        <!-- 5层信号输入 -->
        <mxCell id="signal1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="0" y="0" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="5" y="25" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="10" y="50" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="15" y="75" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="signal5" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;opacity=80;" vertex="1" parent="input-stack">
          <mxGeometry x="20" y="100" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 输入标签 -->
        <mxCell id="input-label" value="N路RFID原始信号&#xa;(多天线×多标签)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="30" y="420" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 信号预处理模块 -->
        <mxCell id="preprocessing" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="250" y="180" width="180" height="240" as="geometry" />
        </mxCell>

        <!-- 增强的波形信号预处理图形 -->
        <mxCell id="enhanced-preprocessing-waveform" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="260" y="200" width="160" height="160" as="geometry" />
        </mxCell>

        <!-- 原始噪声信号波形（增强版） -->
        <mxCell id="enhanced-original-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#82B366;strokeWidth=3;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="10" y="40" as="sourcePoint" />
            <mxPoint x="70" y="40" as="targetPoint" />
            <Array as="points">
              <mxPoint x="15" y="25" />
              <mxPoint x="20" y="55" />
              <mxPoint x="25" y="20" />
              <mxPoint x="30" y="60" />
              <mxPoint x="35" y="25" />
              <mxPoint x="40" y="55" />
              <mxPoint x="45" y="30" />
              <mxPoint x="50" y="50" />
              <mxPoint x="55" y="35" />
              <mxPoint x="60" y="45" />
              <mxPoint x="65" y="40" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 滤波处理器（增强版） -->
        <mxCell id="enhanced-filter-processor" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;fillColor=#DAE8FC;" vertex="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry x="80" y="25" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 滤波器内部图形 -->
        <mxCell id="filter-internal-graphic" value="" style="curved=1;endArrow=none;html=1;strokeColor=#6C8EBF;strokeWidth=1;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="85" y="40" as="sourcePoint" />
            <mxPoint x="105" y="40" as="targetPoint" />
            <Array as="points">
              <mxPoint x="90" y="35" />
              <mxPoint x="95" y="45" />
              <mxPoint x="100" y="35" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 处理后的平滑波形（增强版） -->
        <mxCell id="enhanced-processed-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=3;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="40" as="sourcePoint" />
            <mxPoint x="150" y="40" as="targetPoint" />
            <Array as="points">
              <mxPoint x="125" y="35" />
              <mxPoint x="130" y="45" />
              <mxPoint x="135" y="35" />
              <mxPoint x="140" y="45" />
              <mxPoint x="145" y="35" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 差分特征波形（增强版） -->
        <mxCell id="enhanced-diff-feature-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=3;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="10" y="100" as="sourcePoint" />
            <mxPoint x="70" y="100" as="targetPoint" />
            <Array as="points">
              <mxPoint x="20" y="90" />
              <mxPoint x="30" y="110" />
              <mxPoint x="40" y="85" />
              <mxPoint x="50" y="115" />
              <mxPoint x="60" y="95" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 非线性变换后波形（增强版） -->
        <mxCell id="enhanced-nonlinear-wave" value="" style="curved=1;endArrow=none;html=1;strokeColor=#B85450;strokeWidth=3;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="100" as="sourcePoint" />
            <mxPoint x="150" y="100" as="targetPoint" />
            <Array as="points">
              <mxPoint x="125" y="85" />
              <mxPoint x="130" y="110" />
              <mxPoint x="135" y="80" />
              <mxPoint x="140" y="115" />
              <mxPoint x="145" y="90" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 处理流程箭头（增强版） -->
        <mxCell id="enhanced-wave-arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="75" y="40" as="sourcePoint" />
            <mxPoint x="80" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="enhanced-wave-arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="40" as="sourcePoint" />
            <mxPoint x="115" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 垂直连接箭头 -->
        <mxCell id="vertical-connection-arrow" value="" style="endArrow=classic;html=1;strokeColor=#666666;strokeWidth=1;" edge="1" parent="enhanced-preprocessing-waveform">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="80" y="70" as="sourcePoint" />
            <mxPoint x="80" y="85" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 预处理标签 -->
        <mxCell id="preprocessing-label" value="信号预处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="290" y="430" width="100" height="30" as="geometry" />
        </mxCell>

        <!-- 整合的异构图构建与表示学习模块 -->
        <mxCell id="integrated-hetero-module" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="480" y="180" width="420" height="240" as="geometry" />
        </mxCell>

        <!-- 异构图构建部分 -->
        <mxCell id="graph-construction-section" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="500" y="200" width="180" height="200" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag-node1" value="T1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="30" y="30" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="tag-node2" value="T2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="115" y="30" width="35" height="35" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna-node1" value="A1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="30" y="135" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="antenna-node2" value="A2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="graph-construction-section">
          <mxGeometry x="115" y="135" width="35" height="35" as="geometry" />
        </mxCell>

        <!-- 边关系 -->
        <!-- T-T边 (标签-标签) -->
        <mxCell id="edge-tt" value="" style="endArrow=none;html=1;strokeColor=#666666;strokeWidth=2;dashed=0;" edge="1" parent="graph-construction-section" source="tag-node1" target="tag-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="50" as="sourcePoint" />
            <mxPoint x="110" y="50" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T-A边 (标签-天线) -->
        <mxCell id="edge-ta1" value="" style="endArrow=classic;html=1;strokeColor=#0066CC;strokeWidth=2;" edge="1" parent="graph-construction-section" source="tag-node1" target="antenna-node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="50" y="70" as="sourcePoint" />
            <mxPoint x="50" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="edge-ta2" value="" style="endArrow=classic;html=1;strokeColor=#0066CC;strokeWidth=2;" edge="1" parent="graph-construction-section" source="tag-node2" target="antenna-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="135" y="70" as="sourcePoint" />
            <mxPoint x="135" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-T边 (天线-标签) -->
        <mxCell id="edge-at1" value="" style="endArrow=classic;html=1;strokeColor=#66B2FF;strokeWidth=1;dashed=1;" edge="1" parent="graph-construction-section" source="antenna-node1" target="tag-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="140" as="sourcePoint" />
            <mxPoint x="110" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-A边 (天线-天线) -->
        <mxCell id="edge-aa" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=1;dashed=1;dashPattern=5 5;" edge="1" parent="graph-construction-section" source="antenna-node1" target="antenna-node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="155" as="sourcePoint" />
            <mxPoint x="110" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>



        <!-- 重新设计的异构图表示学习部分 -->
        <mxCell id="redesigned-hetero-learning" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="700" y="200" width="180" height="200" as="geometry" />
        </mxCell>

        <!-- 输入特征流 -->
        <mxCell id="input-feature-flow" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;fillColor=#DAE8FC;gradientColor=#B3D9FF;gradientDirection=east;" vertex="1" parent="redesigned-hetero-learning">
          <mxGeometry x="10" y="20" width="160" height="12" as="geometry" />
        </mxCell>

        <!-- 第一层学习过程 -->
        <mxCell id="layer1-learning-process" value="" style="group" vertex="1" connectable="0" parent="redesigned-hetero-learning">
          <mxGeometry x="20" y="50" width="140" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力权重计算可视化 -->
        <mxCell id="attention-computation" value="" style="ellipse;whiteSpace=wrap;html=1;strokeColor=#D79B00;fillColor=#FFF2CC;gradientColor=#FFE6CC;gradientDirection=radial;" vertex="1" parent="layer1-learning-process">
          <mxGeometry x="10" y="5" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 特征聚合过程 -->
        <mxCell id="feature-aggregation-process" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#9673A6;fillColor=#E1D5E7;gradientColor=#C9B3E0;gradientDirection=east;" vertex="1" parent="layer1-learning-process">
          <mxGeometry x="50" y="10" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力到聚合的连接 -->
        <mxCell id="attention-to-aggregation" value="" style="endArrow=classic;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="layer1-learning-process">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="20" as="sourcePoint" />
            <mxPoint x="50" y="20" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 第二层学习过程 -->
        <mxCell id="layer2-learning-process" value="" style="group" vertex="1" connectable="0" parent="redesigned-hetero-learning">
          <mxGeometry x="20" y="110" width="140" height="40" as="geometry" />
        </mxCell>

        <!-- 注意力权重计算可视化 -->
        <mxCell id="attention-computation2" value="" style="ellipse;whiteSpace=wrap;html=1;strokeColor=#D79B00;fillColor=#FFF2CC;gradientColor=#FFE6CC;gradientDirection=radial;" vertex="1" parent="layer2-learning-process">
          <mxGeometry x="10" y="5" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 特征聚合过程 -->
        <mxCell id="feature-aggregation-process2" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#9673A6;fillColor=#E1D5E7;gradientColor=#C9B3E0;gradientDirection=east;" vertex="1" parent="layer2-learning-process">
          <mxGeometry x="50" y="10" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力到聚合的连接 -->
        <mxCell id="attention-to-aggregation2" value="" style="endArrow=classic;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="layer2-learning-process">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="20" as="sourcePoint" />
            <mxPoint x="50" y="20" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出特征流 -->
        <mxCell id="output-feature-flow" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#82B366;fillColor=#D5E8D4;gradientColor=#A8D4A8;gradientDirection=east;" vertex="1" parent="redesigned-hetero-learning">
          <mxGeometry x="40" y="170" width="100" height="12" as="geometry" />
        </mxCell>

        <!-- 学习流程的主要数据流箭头 -->
        <mxCell id="main-learning-flow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="redesigned-hetero-learning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="35" as="sourcePoint" />
            <mxPoint x="90" y="45" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="main-learning-flow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="redesigned-hetero-learning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="95" as="sourcePoint" />
            <mxPoint x="90" y="105" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="main-learning-flow3" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="redesigned-hetero-learning">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="155" as="sourcePoint" />
            <mxPoint x="90" y="165" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力可视化 -->
        <mxCell id="multi-head-attention-viz" value="" style="group" vertex="1" connectable="0" parent="redesigned-hetero-learning">
          <mxGeometry x="150" y="55" width="25" height="80" as="geometry" />
        </mxCell>

        <!-- 注意力头1 -->
        <mxCell id="head1-viz" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="multi-head-attention-viz">
          <mxGeometry x="5" y="0" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头2 -->
        <mxCell id="head2-viz" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="multi-head-attention-viz">
          <mxGeometry x="5" y="20" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头3 -->
        <mxCell id="head3-viz" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="multi-head-attention-viz">
          <mxGeometry x="5" y="40" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头4 -->
        <mxCell id="head4-viz" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;" vertex="1" parent="multi-head-attention-viz">
          <mxGeometry x="5" y="60" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 多头注意力连接线 -->
        <mxCell id="head-connection1" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=1;" edge="1" parent="multi-head-attention-viz">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="0" y="7" as="sourcePoint" />
            <mxPoint x="5" y="7" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="head-connection2" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=1;" edge="1" parent="multi-head-attention-viz">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="0" y="27" as="sourcePoint" />
            <mxPoint x="5" y="27" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="head-connection3" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=1;" edge="1" parent="multi-head-attention-viz">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="0" y="47" as="sourcePoint" />
            <mxPoint x="5" y="47" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="head-connection4" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=1;" edge="1" parent="multi-head-attention-viz">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="0" y="67" as="sourcePoint" />
            <mxPoint x="5" y="67" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 最终输出特征 -->
        <mxCell id="hetero-output" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#82B366;fillColor=#D5E8D4;gradientColor=#A8D4A8;gradientDirection=south;" vertex="1" parent="redesigned-hetero-learning">
          <mxGeometry x="65" y="185" width="50" height="15" as="geometry" />
        </mxCell>

        <!-- 图构建到学习的连接箭头 -->
        <mxCell id="construction-to-learning" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="680" y="300" as="sourcePoint" />
            <mxPoint x="700" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 整合模块标签 -->
        <mxCell id="integrated-module-label" value="异构图构建与表示学习&#xa;(标签-天线二元图 + HGATv2)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="620" y="430" width="180" height="40" as="geometry" />
        </mxCell>



        <!-- 多模型融合定位预测模块 -->
        <mxCell id="fusion-prediction-module" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="960" y="180" width="330" height="240" as="geometry" />
        </mxCell>



        <!-- 分隔线 -->
        <mxCell id="separation-line" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=1;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="810" y="280" as="sourcePoint" />
            <mxPoint x="1000" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- MLP网络结构 (移动到融合模块) -->
        <!-- 输入层 -->
        <mxCell id="mlp-input1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="980" y="200" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-input2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="980" y="230" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-input3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#DAE8FC;strokeColor=#6C8EBF;" vertex="1" parent="1">
          <mxGeometry x="980" y="260" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 隐藏层1 -->
        <mxCell id="mlp-hidden1-1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1040" y="190" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1040" y="210" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1040" y="230" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1040" y="250" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden1-5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1040" y="270" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 隐藏层2 -->
        <mxCell id="mlp-hidden2-1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1090" y="200" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1090" y="220" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1090" y="240" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="mlp-hidden2-4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="1090" y="260" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 输出层 -->
        <mxCell id="mlp-output1" value="x" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1140" y="220" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="mlp-output2" value="y" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1140" y="240" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- MLP全连接网络连接线 -->
        <!-- 输入层到隐藏层1的连接 -->
        <mxCell id="mlp-conn-i1-h11" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h12" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h13" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h14" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i1-h15" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input1" target="mlp-hidden1-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-i2-h11" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h12" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h13" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h14" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i2-h15" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input2" target="mlp-hidden1-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-i3-h11" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h12" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h13" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h14" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-i3-h15" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-input3" target="mlp-hidden1-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 隐藏层1到隐藏层2的连接 -->
        <mxCell id="mlp-conn-h11-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h11-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h11-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h11-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-1" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h12-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h12-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h12-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h12-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-2" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h13-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h13-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h13-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h13-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-3" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h14-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h14-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h14-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h14-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-4" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h15-h21" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h15-h22" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h15-h23" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h15-h24" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden1-5" target="mlp-hidden2-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 隐藏层2到输出层的连接 -->
        <mxCell id="mlp-conn-h21-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-1" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h21-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-1" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h22-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-2" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h22-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-2" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h23-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-3" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h23-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-3" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="mlp-conn-h24-o1" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-4" target="mlp-output1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mlp-conn-h24-o2" value="" style="endArrow=none;html=1;strokeColor=#CCCCCC;strokeWidth=0.5;" edge="1" parent="1" source="mlp-hidden2-4" target="mlp-output2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 异构图注意力网络路径 -->
        <mxCell id="gat-path" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="820" y="300" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- GAT网络表示 -->
        <mxCell id="gat-network" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;fillColor=#DAE8FC;" vertex="1" parent="gat-path">
          <mxGeometry x="10" y="10" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 注意力头表示 -->
        <mxCell id="attention-head1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="gat-path">
          <mxGeometry x="15" y="50" width="12" height="12" as="geometry" />
        </mxCell>
        <mxCell id="attention-head2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="gat-path">
          <mxGeometry x="35" y="50" width="12" height="12" as="geometry" />
        </mxCell>
        <mxCell id="attention-head3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;" vertex="1" parent="gat-path">
          <mxGeometry x="55" y="50" width="12" height="12" as="geometry" />
        </mxCell>

        <!-- GAT输出 -->
        <mxCell id="gat-output" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#6C8EBF;fillColor=#D5E8D4;" vertex="1" parent="gat-path">
          <mxGeometry x="80" y="20" width="25" height="15" as="geometry" />
        </mxCell>

        <!-- 融合节点 -->
        <mxCell id="fusion-node" value="⊕" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;fontSize=16;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1180" y="280" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- MLP路径到融合节点的连接 -->
        <mxCell id="mlp-to-fusion" value="" style="endArrow=classic;html=1;strokeColor=#B85450;strokeWidth=2;" edge="1" parent="1" source="mlp-output1" target="fusion-node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1160" y="230" as="sourcePoint" />
            <mxPoint x="1180" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- GAT路径到融合节点的连接 -->
        <mxCell id="gat-to-fusion" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=2;" edge="1" parent="1" source="hetero-output" target="fusion-node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="385" as="sourcePoint" />
            <mxPoint x="1180" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 最终坐标输出 -->
        <mxCell id="final-x-output" value="x" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1240" y="270" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="final-y-output" value="y" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="1240" y="305" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 融合到最终输出的连接 -->
        <mxCell id="fusion-to-x-output" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=2;" edge="1" parent="1" source="fusion-node" target="final-x-output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1210" y="295" as="sourcePoint" />
            <mxPoint x="1240" y="282" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="fusion-to-y-output" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=2;" edge="1" parent="1" source="fusion-node" target="final-y-output">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1210" y="295" as="sourcePoint" />
            <mxPoint x="1240" y="317" as="targetPoint" />
          </mxGeometry>
        </mxCell>



        <!-- 多模型融合定位预测标签 -->
        <mxCell id="fusion-prediction-label" value="多模型融合定位预测" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="890" y="430" width="140" height="30" as="geometry" />
        </mxCell>

        <!-- 流程箭头 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="190" y="300" as="sourcePoint" />
            <mxPoint x="240" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="300" as="sourcePoint" />
            <mxPoint x="490" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="300" as="sourcePoint" />
            <mxPoint x="470" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;strokeColor=#6C8EBF;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="910" y="300" as="sourcePoint" />
            <mxPoint x="950" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 箭头标注 -->
        <mxCell id="arrow1-label" value="滤波/对齐/&#xa;多尺度特征化" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="190" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow2-label" value="构建二元异构图&#xa;四类边关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="440" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow3-label" value="异构图构建&#xa;与表示学习" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="420" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="arrow4-label" value="多模型融合&#xa;定位预测" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="910" y="270" width="70" height="25" as="geometry" />
        </mxCell>

        <!-- 图题 -->
        <mxCell id="figure-title" value="图1 RFID静态定位的异构图注意力管线：从多路RSSI信号预处理，经T-T/T-A/A-T/A-A边集合的二元异构图构建，到HGATv2表示学习与多模型融合定位预测" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontWeight=bold;" vertex="1" parent="1">
          <mxGeometry x="200" y="500" width="900" height="40" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
